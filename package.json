{"name": "yes", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "prepare": "cypress install", "test:e2e": "playwright test", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@playwright/test": "^1.51.1", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "cypress": "^14.2.1", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "postcss": "^8.5.5", "start-server-and-test": "^2.0.11", "tailwindcss": "^3.4.0", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}